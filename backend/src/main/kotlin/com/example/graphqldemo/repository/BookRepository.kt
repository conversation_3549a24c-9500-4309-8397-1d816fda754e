package com.example.graphqldemo.repository

import com.example.graphqldemo.model.Book
import com.example.graphqldemo.model.BookInput
import com.example.graphqldemo.model.UpdateBookInput
import org.springframework.stereotype.Repository
import java.util.*
import java.util.concurrent.ConcurrentHashMap

@Repository
class BookRepository {
    
    private val books = ConcurrentHashMap<String, Book>()
    
    init {
        // Initialize with some sample data
        val sampleBooks = listOf(
            Book(
                id = "1",
                title = "The Kotlin Programming Language",
                author = "JetBrains Team",
                isbn = "978-0123456789",
                publishedYear = 2023,
                genre = "Programming",
                description = "A comprehensive guide to Kotlin programming language"
            ),
            Book(
                id = "2",
                title = "Spring Boot in Action",
                author = "Craig Walls",
                isbn = "978-1617292545",
                publishedYear = 2022,
                genre = "Programming",
                description = "Learn Spring Boot framework with practical examples"
            ),
            Book(
                id = "3",
                title = "GraphQL: The Complete Guide",
                author = "<PERSON>",
                isbn = "978-1492030713",
                publishedYear = 2021,
                genre = "Programming",
                description = "Master GraphQL for modern API development"
            )
        )
        
        sampleBooks.forEach { book ->
            books[book.id] = book
        }
    }
    
    fun findAll(): List<Book> {
        return books.values.toList()
    }
    
    fun findById(id: String): Book? {
        return books[id]
    }
    
    fun save(bookInput: BookInput): Book {
        val id = UUID.randomUUID().toString()
        val book = Book(
            id = id,
            title = bookInput.title,
            author = bookInput.author,
            isbn = bookInput.isbn,
            publishedYear = bookInput.publishedYear,
            genre = bookInput.genre,
            description = bookInput.description
        )
        books[id] = book
        return book
    }
    
    fun update(updateInput: UpdateBookInput): Book? {
        val existingBook = books[updateInput.id] ?: return null
        
        val updatedBook = existingBook.copy(
            title = updateInput.title ?: existingBook.title,
            author = updateInput.author ?: existingBook.author,
            isbn = updateInput.isbn ?: existingBook.isbn,
            publishedYear = updateInput.publishedYear ?: existingBook.publishedYear,
            genre = updateInput.genre ?: existingBook.genre,
            description = updateInput.description ?: existingBook.description
        )
        
        books[updateInput.id] = updatedBook
        return updatedBook
    }
    
    fun deleteById(id: String): Boolean {
        return books.remove(id) != null
    }
    
    fun searchByTitleOrAuthor(query: String): List<Book> {
        val searchQuery = query.lowercase()
        return books.values.filter { book ->
            book.title.lowercase().contains(searchQuery) || 
            book.author.lowercase().contains(searchQuery)
        }
    }
}
