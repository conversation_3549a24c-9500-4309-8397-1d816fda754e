package com.example.graphqldemo.repository

import com.example.graphqldemo.model.Car
import com.example.graphqldemo.model.CarInput
import com.example.graphqldemo.model.UpdateCarInput
import org.springframework.stereotype.Repository
import java.util.*
import java.util.concurrent.ConcurrentHashMap

@Repository
class CarRepository {

    private val cars = ConcurrentHashMap<String, Car>()
    
    init {
        // Initialize with some sample data
        val sampleCars = listOf(
            Car(
                id = "1",
                manufacturer = "Toyota",
                name = "Camry"
            ),
            Car(
                id = "2",
                manufacturer = "Honda",
                name = "Civic"
            ),
            Car(
                id = "3",
                manufacturer = "BMW",
                name = "X5"
            ),
            Car(
                id = "4",
                manufacturer = "Mercedes-Benz",
                name = "C-Class"
            ),
            Car(
                id = "5",
                manufacturer = "Audi",
                name = "A4"
            )
        )

        sampleCars.forEach { car ->
            cars[car.id] = car
        }
    }
    
    fun findAll(): List<Car> {
        return cars.values.toList()
    }

    fun findById(id: String): Car? {
        return cars[id]
    }

    fun save(carInput: CarInput): Car {
        val id = UUID.randomUUID().toString()
        val car = Car(
            id = id,
            manufacturer = carInput.manufacturer,
            name = carInput.name
        )
        cars[id] = car
        return car
    }

    fun update(updateInput: UpdateCarInput): Car? {
        val existingCar = cars[updateInput.id] ?: return null

        val updatedCar = existingCar.copy(
            manufacturer = updateInput.manufacturer ?: existingCar.manufacturer,
            name = updateInput.name ?: existingCar.name
        )

        cars[updateInput.id] = updatedCar
        return updatedCar
    }

    fun deleteById(id: String): Boolean {
        return cars.remove(id) != null
    }

    fun searchByManufacturerOrName(query: String): List<Car> {
        val searchQuery = query.lowercase()
        return cars.values.filter { car ->
            car.manufacturer.lowercase().contains(searchQuery) ||
            car.name.lowercase().contains(searchQuery)
        }
    }
}
