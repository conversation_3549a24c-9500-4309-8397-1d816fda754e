package com.example.graphqldemo.model

data class Book(
    val id: String,
    val title: String,
    val author: String,
    val isbn: String? = null,
    val publishedYear: Int? = null,
    val genre: String? = null,
    val description: String? = null
)

data class BookInput(
    val title: String,
    val author: String,
    val isbn: String? = null,
    val publishedYear: Int? = null,
    val genre: String? = null,
    val description: String? = null
)

data class UpdateBookInput(
    val id: String,
    val title: String? = null,
    val author: String? = null,
    val isbn: String? = null,
    val publishedYear: Int? = null,
    val genre: String? = null,
    val description: String? = null
)
