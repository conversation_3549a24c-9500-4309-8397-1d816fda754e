package com.example.graphqldemo.resolver

import com.example.graphqldemo.model.Car
import com.example.graphqldemo.model.CarInput
import com.example.graphqldemo.model.UpdateCarInput
import com.example.graphqldemo.repository.CarRepository
import org.springframework.graphql.data.method.annotation.Argument
import org.springframework.graphql.data.method.annotation.MutationMapping
import org.springframework.stereotype.Controller

@Controller
class CarMutationResolver(
    private val carRepository: CarRepository
) {

    @MutationMapping
    fun addCar(@Argument input: CarInput): Car {
        return carRepository.save(input)
    }

    @MutationMapping
    fun updateCar(@Argument input: UpdateCarInput): Car? {
        return carRepository.update(input)
    }

    @MutationMapping
    fun deleteCar(@Argument id: String): Boolean {
        return carRepository.deleteById(id)
    }
}
