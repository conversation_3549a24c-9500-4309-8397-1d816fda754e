package com.example.graphqldemo.resolver

import com.example.graphqldemo.model.Car
import com.example.graphqldemo.repository.CarRepository
import org.springframework.graphql.data.method.annotation.Argument
import org.springframework.graphql.data.method.annotation.QueryMapping
import org.springframework.stereotype.Controller

@Controller
class CarQueryResolver(
    private val carRepository: CarRepository
) {

    @QueryMapping
    fun cars(): List<Car> {
        return carRepository.findAll()
    }

    @QueryMapping
    fun car(@Argument id: String): Car? {
        return carRepository.findById(id)
    }

    @QueryMapping
    fun searchCars(@Argument query: String): List<Car> {
        return carRepository.searchByManufacturerOrName(query)
    }
}
