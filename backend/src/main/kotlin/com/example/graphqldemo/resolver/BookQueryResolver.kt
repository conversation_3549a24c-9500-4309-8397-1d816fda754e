package com.example.graphqldemo.resolver

import com.example.graphqldemo.model.Book
import com.example.graphqldemo.repository.BookRepository
import org.springframework.graphql.data.method.annotation.Argument
import org.springframework.graphql.data.method.annotation.QueryMapping
import org.springframework.stereotype.Controller

@Controller
class BookQueryResolver(
    private val bookRepository: BookRepository
) {
    
    @QueryMapping
    fun books(): List<Book> {
        return bookRepository.findAll()
    }
    
    @QueryMapping
    fun book(@Argument id: String): Book? {
        return bookRepository.findById(id)
    }
    
    @QueryMapping
    fun searchBooks(@Argument query: String): List<Book> {
        return bookRepository.searchByTitleOrAuthor(query)
    }
}
