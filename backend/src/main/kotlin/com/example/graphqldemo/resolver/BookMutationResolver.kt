package com.example.graphqldemo.resolver

import com.example.graphqldemo.model.Book
import com.example.graphqldemo.model.BookInput
import com.example.graphqldemo.model.UpdateBookInput
import com.example.graphqldemo.repository.BookRepository
import org.springframework.graphql.data.method.annotation.Argument
import org.springframework.graphql.data.method.annotation.MutationMapping
import org.springframework.stereotype.Controller

@Controller
class BookMutationResolver(
    private val bookRepository: BookRepository
) {
    
    @MutationMapping
    fun addBook(@Argument input: BookInput): Book {
        return bookRepository.save(input)
    }
    
    @MutationMapping
    fun updateBook(@Argument input: UpdateBookInput): Book? {
        return bookRepository.update(input)
    }
    
    @MutationMapping
    fun deleteBook(@Argument id: String): Boolean {
        return bookRepository.deleteById(id)
    }
}
