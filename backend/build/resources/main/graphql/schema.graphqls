type Book {
    id: ID!
    title: String!
    author: String!
    isbn: String
    publishedYear: Int
    genre: String
    description: String
}

input BookInput {
    title: String!
    author: String!
    isbn: String
    publishedYear: Int
    genre: String
    description: String
}

input UpdateBookInput {
    id: ID!
    title: String
    author: String
    isbn: String
    publishedYear: Int
    genre: String
    description: String
}

type Query {
    # Get all books
    books: [Book!]!
    
    # Get a book by ID
    book(id: ID!): Book
    
    # Search books by title or author
    searchBooks(query: String!): [Book!]!
}

type Mutation {
    # Add a new book
    addBook(input: BookInput!): Book!
    
    # Update an existing book
    updateBook(input: UpdateBookInput!): Book
    
    # Delete a book by ID
    deleteBook(id: ID!): Boolean!
}
