type Car {
    id: ID!
    manufacturer: String!
    name: String!
}

input CarInput {
    manufacturer: String!
    name: String!
}

input UpdateCarInput {
    id: ID!
    manufacturer: String
    name: String
}

type Query {
    # Get all cars
    cars: [Car!]!

    # Get a car by ID
    car(id: ID!): Car

    # Search cars by manufacturer or name
    searchCars(query: String!): [Car!]!
}

type Mutation {
    # Add a new car
    addCar(input: CarInput!): Car!

    # Update an existing car
    updateCar(input: UpdateCarInput!): Car

    # Delete a car by ID
    deleteCar(id: ID!): Boolean!
}
