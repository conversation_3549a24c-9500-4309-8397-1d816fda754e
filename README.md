# GraphQL Demo Project

This project demonstrates GraphQL usage with:
- **Backend**: Kotlin + Spring Boot + Spring GraphQL
- **Frontend**: React + TypeScript + Apollo Client

## Project Structure

```
graphql2/
├── backend/          # Kotlin Spring Boot GraphQL API
└── frontend/         # React Apollo Client App
```

## Features

- GraphQL schema with queries and mutations
- Book management system (CRUD operations)
- Real-time data synchronization between frontend and backend

## Getting Started

### Backend (Kotlin + Spring Boot)
```bash
cd backend
./gradlew bootRun
```

### Frontend (React + Apollo)
```bash
cd frontend
npm install
npm start
```

## GraphQL Operations

- **Query**: Get all books, get book by ID
- **Mutation**: Add new book, update existing book

The GraphQL playground will be available at `http://localhost:8080/graphiql`
