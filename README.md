# GraphQL Demo Project - Car Management

This project demonstrates GraphQL usage with:
- **Backend**: Kotlin + Spring Boot + Spring GraphQL
- **Frontend**: React + TypeScript + Apollo Client

## Project Structure

```
graphql2/
├── backend/          # Kotlin Spring Boot GraphQL API
└── frontend/         # React Apollo Client App
```

## Features

- GraphQL schema with queries and mutations
- Simple car management system (CRUD operations)
- Real-time data synchronization between frontend and backend
- Clean and minimal interface with just manufacturer and name fields

## Getting Started

### Backend (Kotlin + Spring Boot)
```bash
cd backend
./gradlew bootRun
```

### Frontend (React + Apollo)
```bash
cd frontend
npm install
npm start
```

## GraphQL Operations

- **Query**: Get all cars, get car by ID, search cars
- **Mutation**: Add new car, update existing car, delete car

The GraphQL playground will be available at `http://localhost:8080/graphiql`

## Sample Operations

### Get all cars:
```graphql
query {
  cars {
    id
    manufacturer
    name
  }
}
```

### Add a new car:
```graphql
mutation {
  addCar(input: {
    manufacturer: "Tesla"
    name: "Model S"
  }) {
    id
    manufacturer
    name
  }
}
```
