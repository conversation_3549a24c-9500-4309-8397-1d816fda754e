export interface Book {
  id: string;
  title: string;
  author: string;
  isbn?: string;
  publishedYear?: number;
  genre?: string;
  description?: string;
}

export interface BookInput {
  title: string;
  author: string;
  isbn?: string;
  publishedYear?: number;
  genre?: string;
  description?: string;
}

export interface UpdateBookInput {
  id: string;
  title?: string;
  author?: string;
  isbn?: string;
  publishedYear?: number;
  genre?: string;
  description?: string;
}
