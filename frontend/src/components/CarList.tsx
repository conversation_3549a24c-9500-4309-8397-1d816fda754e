import React, { useState } from 'react';
import { useQuery, useMutation } from '@apollo/client';
import { GET_CARS, SEARCH_CARS } from '../graphql/queries';
import { DELETE_CAR } from '../graphql/mutations';
import { Car } from '../types/Car';
import CarForm from './CarForm';
import './CarList.css';

const CarList: React.FC = () => {
  const [searchQuery, setSearchQuery] = useState('');
  const [editingCar, setEditingCar] = useState<Car | null>(null);
  const [showAddForm, setShowAddForm] = useState(false);

  const { loading, error, data, refetch } = useQuery(GET_CARS);
  const { data: searchData, loading: searchLoading } = useQuery(SEARCH_CARS, {
    variables: { query: searchQuery },
    skip: !searchQuery
  });

  const [deleteCar] = useMutation(DELETE_CAR, {
    refetchQueries: [{ query: GET_CARS }]
  });

  const handleDelete = async (id: string) => {
    if (window.confirm('Are you sure you want to delete this car?')) {
      try {
        await deleteCar({ variables: { id } });
      } catch (error) {
        console.error('Error deleting car:', error);
      }
    }
  };

  const handleEdit = (car: Car) => {
    setEditingCar(car);
    setShowAddForm(false);
  };

  const handleAddNew = () => {
    setShowAddForm(true);
    setEditingCar(null);
  };

  const handleFormClose = () => {
    setShowAddForm(false);
    setEditingCar(null);
    refetch();
  };

  if (loading) return <div className="loading">Loading cars...</div>;
  if (error) return <div className="error">Error loading cars: {error.message}</div>;

  const carsToDisplay = searchQuery ? (searchData?.searchCars || []) : (data?.cars || []);

  return (
    <div className="car-list-container">
      <div className="header">
        <h1>Car Garage</h1>
        <div className="controls">
          <input
            type="text"
            placeholder="Search cars by manufacturer or name..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="search-input"
          />
          <button onClick={handleAddNew} className="add-button">
            Add New Car
          </button>
        </div>
      </div>

      {searchLoading && <div className="loading">Searching...</div>}

      <div className="cars-grid">
        {carsToDisplay.map((car: Car) => (
          <div key={car.id} className="car-card">
            <h3>{car.name}</h3>
            <p className="manufacturer">by {car.manufacturer}</p>

            <div className="car-actions">
              <button onClick={() => handleEdit(car)} className="edit-button">
                Edit
              </button>
              <button onClick={() => handleDelete(car.id)} className="delete-button">
                Delete
              </button>
            </div>
          </div>
        ))}
      </div>

      {carsToDisplay.length === 0 && !loading && !searchLoading && (
        <div className="no-cars">
          {searchQuery ? 'No cars found matching your search.' : 'No cars available.'}
        </div>
      )}

      {(showAddForm || editingCar) && (
        <CarForm
          car={editingCar}
          onClose={handleFormClose}
        />
      )}
    </div>
  );
};

export default CarList;
