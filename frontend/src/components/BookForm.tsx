import React, { useState, useEffect } from 'react';
import { useMutation } from '@apollo/client';
import { ADD_BOOK, UPDATE_BOOK } from '../graphql/mutations';
import { GET_BOOKS } from '../graphql/queries';
import { Book, BookInput, UpdateBookInput } from '../types/Book';
import './BookForm.css';

interface BookFormProps {
  book?: Book | null;
  onClose: () => void;
}

const BookForm: React.FC<BookFormProps> = ({ book, onClose }) => {
  const [formData, setFormData] = useState({
    title: '',
    author: '',
    isbn: '',
    publishedYear: '',
    genre: '',
    description: ''
  });

  const [addBook, { loading: addLoading }] = useMutation(ADD_BOOK, {
    refetchQueries: [{ query: GET_BOOKS }]
  });

  const [updateBook, { loading: updateLoading }] = useMutation(UPDATE_BOOK, {
    refetchQueries: [{ query: GET_BOOKS }]
  });

  useEffect(() => {
    if (book) {
      setFormData({
        title: book.title || '',
        author: book.author || '',
        isbn: book.isbn || '',
        publishedYear: book.publishedYear?.toString() || '',
        genre: book.genre || '',
        description: book.description || ''
      });
    }
  }, [book]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    try {
      if (book) {
        // Update existing book
        const updateInput: UpdateBookInput = {
          id: book.id,
          title: formData.title || undefined,
          author: formData.author || undefined,
          isbn: formData.isbn || undefined,
          publishedYear: formData.publishedYear ? parseInt(formData.publishedYear) : undefined,
          genre: formData.genre || undefined,
          description: formData.description || undefined
        };
        
        await updateBook({ variables: { input: updateInput } });
      } else {
        // Add new book
        const bookInput: BookInput = {
          title: formData.title,
          author: formData.author,
          isbn: formData.isbn || undefined,
          publishedYear: formData.publishedYear ? parseInt(formData.publishedYear) : undefined,
          genre: formData.genre || undefined,
          description: formData.description || undefined
        };
        
        await addBook({ variables: { input: bookInput } });
      }
      
      onClose();
    } catch (error) {
      console.error('Error saving book:', error);
    }
  };

  const isLoading = addLoading || updateLoading;

  return (
    <div className="modal-overlay">
      <div className="modal-content">
        <div className="modal-header">
          <h2>{book ? 'Edit Book' : 'Add New Book'}</h2>
          <button onClick={onClose} className="close-button">×</button>
        </div>
        
        <form onSubmit={handleSubmit} className="book-form">
          <div className="form-group">
            <label htmlFor="title">Title *</label>
            <input
              type="text"
              id="title"
              name="title"
              value={formData.title}
              onChange={handleChange}
              required
            />
          </div>

          <div className="form-group">
            <label htmlFor="author">Author *</label>
            <input
              type="text"
              id="author"
              name="author"
              value={formData.author}
              onChange={handleChange}
              required
            />
          </div>

          <div className="form-group">
            <label htmlFor="isbn">ISBN</label>
            <input
              type="text"
              id="isbn"
              name="isbn"
              value={formData.isbn}
              onChange={handleChange}
            />
          </div>

          <div className="form-group">
            <label htmlFor="publishedYear">Published Year</label>
            <input
              type="number"
              id="publishedYear"
              name="publishedYear"
              value={formData.publishedYear}
              onChange={handleChange}
              min="1000"
              max="2024"
            />
          </div>

          <div className="form-group">
            <label htmlFor="genre">Genre</label>
            <input
              type="text"
              id="genre"
              name="genre"
              value={formData.genre}
              onChange={handleChange}
            />
          </div>

          <div className="form-group">
            <label htmlFor="description">Description</label>
            <textarea
              id="description"
              name="description"
              value={formData.description}
              onChange={handleChange}
              rows={4}
            />
          </div>

          <div className="form-actions">
            <button type="button" onClick={onClose} className="cancel-button">
              Cancel
            </button>
            <button type="submit" disabled={isLoading} className="submit-button">
              {isLoading ? 'Saving...' : (book ? 'Update Book' : 'Add Book')}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default BookForm;
