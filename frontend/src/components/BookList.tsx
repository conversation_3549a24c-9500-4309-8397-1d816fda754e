import React, { useState } from 'react';
import { useQuery, useMutation } from '@apollo/client';
import { GET_BOOKS, SEARCH_BOOKS } from '../graphql/queries';
import { DELETE_BOOK } from '../graphql/mutations';
import { Book } from '../types/Book';
import BookForm from './BookForm';
import './BookList.css';

const BookList: React.FC = () => {
  const [searchQuery, setSearchQuery] = useState('');
  const [editingBook, setEditingBook] = useState<Book | null>(null);
  const [showAddForm, setShowAddForm] = useState(false);

  const { loading, error, data, refetch } = useQuery(GET_BOOKS);
  const { data: searchData, loading: searchLoading } = useQuery(SEARCH_BOOKS, {
    variables: { query: searchQuery },
    skip: !searchQuery
  });

  const [deleteBook] = useMutation(DELETE_BOOK, {
    refetchQueries: [{ query: GET_BOOKS }]
  });

  const handleDelete = async (id: string) => {
    if (window.confirm('Are you sure you want to delete this book?')) {
      try {
        await deleteBook({ variables: { id } });
      } catch (error) {
        console.error('Error deleting book:', error);
      }
    }
  };

  const handleEdit = (book: Book) => {
    setEditingBook(book);
    setShowAddForm(false);
  };

  const handleAddNew = () => {
    setShowAddForm(true);
    setEditingBook(null);
  };

  const handleFormClose = () => {
    setShowAddForm(false);
    setEditingBook(null);
    refetch();
  };

  if (loading) return <div className="loading">Loading books...</div>;
  if (error) return <div className="error">Error loading books: {error.message}</div>;

  const booksToDisplay = searchQuery ? (searchData?.searchBooks || []) : (data?.books || []);

  return (
    <div className="book-list-container">
      <div className="header">
        <h1>Book Library</h1>
        <div className="controls">
          <input
            type="text"
            placeholder="Search books by title or author..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="search-input"
          />
          <button onClick={handleAddNew} className="add-button">
            Add New Book
          </button>
        </div>
      </div>

      {searchLoading && <div className="loading">Searching...</div>}

      <div className="books-grid">
        {booksToDisplay.map((book: Book) => (
          <div key={book.id} className="book-card">
            <h3>{book.title}</h3>
            <p className="author">by {book.author}</p>
            {book.genre && <p className="genre">{book.genre}</p>}
            {book.publishedYear && <p className="year">Published: {book.publishedYear}</p>}
            {book.isbn && <p className="isbn">ISBN: {book.isbn}</p>}
            {book.description && <p className="description">{book.description}</p>}
            
            <div className="book-actions">
              <button onClick={() => handleEdit(book)} className="edit-button">
                Edit
              </button>
              <button onClick={() => handleDelete(book.id)} className="delete-button">
                Delete
              </button>
            </div>
          </div>
        ))}
      </div>

      {booksToDisplay.length === 0 && !loading && !searchLoading && (
        <div className="no-books">
          {searchQuery ? 'No books found matching your search.' : 'No books available.'}
        </div>
      )}

      {(showAddForm || editingBook) && (
        <BookForm
          book={editingBook}
          onClose={handleFormClose}
        />
      )}
    </div>
  );
};

export default BookList;
