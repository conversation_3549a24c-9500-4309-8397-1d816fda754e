import React, { useState, useEffect } from 'react';
import { useMutation } from '@apollo/client';
import { ADD_CAR, UPDATE_CAR } from '../graphql/mutations';
import { GET_CARS } from '../graphql/queries';
import { Car, CarInput, UpdateCarInput } from '../types/Car';
import './CarForm.css';

interface CarFormProps {
  car?: Car | null;
  onClose: () => void;
}

const CarForm: React.FC<CarFormProps> = ({ car, onClose }) => {
  const [formData, setFormData] = useState({
    manufacturer: '',
    name: ''
  });

  const [addCar, { loading: addLoading }] = useMutation(ADD_CAR, {
    refetchQueries: [{ query: GET_CARS }]
  });

  const [updateCar, { loading: updateLoading }] = useMutation(UPDATE_CAR, {
    refetchQueries: [{ query: GET_CARS }]
  });

  useEffect(() => {
    if (car) {
      setFormData({
        manufacturer: car.manufacturer || '',
        name: car.name || ''
      });
    }
  }, [car]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    try {
      if (car) {
        // Update existing car
        const updateInput: UpdateCarInput = {
          id: car.id,
          manufacturer: formData.manufacturer || undefined,
          name: formData.name || undefined
        };

        await updateCar({ variables: { input: updateInput } });
      } else {
        // Add new car
        const carInput: CarInput = {
          manufacturer: formData.manufacturer,
          name: formData.name
        };

        await addCar({ variables: { input: carInput } });
      }

      onClose();
    } catch (error) {
      console.error('Error saving car:', error);
    }
  };

  const isLoading = addLoading || updateLoading;

  return (
    <div className="modal-overlay">
      <div className="modal-content">
        <div className="modal-header">
          <h2>{car ? 'Edit Car' : 'Add New Car'}</h2>
          <button onClick={onClose} className="close-button">×</button>
        </div>

        <form onSubmit={handleSubmit} className="car-form">
          <div className="form-group">
            <label htmlFor="manufacturer">Manufacturer *</label>
            <input
              type="text"
              id="manufacturer"
              name="manufacturer"
              value={formData.manufacturer}
              onChange={handleChange}
              required
            />
          </div>

          <div className="form-group">
            <label htmlFor="name">Name *</label>
            <input
              type="text"
              id="name"
              name="name"
              value={formData.name}
              onChange={handleChange}
              required
            />
          </div>

          <div className="form-actions">
            <button type="button" onClick={onClose} className="cancel-button">
              Cancel
            </button>
            <button type="submit" disabled={isLoading} className="submit-button">
              {isLoading ? 'Saving...' : (car ? 'Update Car' : 'Add Car')}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default CarForm;
