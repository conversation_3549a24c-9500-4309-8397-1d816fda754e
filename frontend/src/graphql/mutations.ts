import { gql } from '@apollo/client';

export const ADD_CAR = gql`
  mutation AddCar($input: CarInput!) {
    addCar(input: $input) {
      id
      manufacturer
      name
    }
  }
`;

export const UPDATE_CAR = gql`
  mutation UpdateCar($input: UpdateCarInput!) {
    updateCar(input: $input) {
      id
      manufacturer
      name
    }
  }
`;

export const DELETE_CAR = gql`
  mutation DeleteCar($id: ID!) {
    deleteCar(id: $id)
  }
`;
