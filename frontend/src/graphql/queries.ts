import { gql } from '@apollo/client';

export const GET_CARS = gql`
  query GetCars {
    cars {
      id
      manufacturer
      name
    }
  }
`;

export const GET_CAR = gql`
  query GetCar($id: ID!) {
    car(id: $id) {
      id
      manufacturer
      name
    }
  }
`;

export const SEARCH_CARS = gql`
  query SearchCars($query: String!) {
    searchCars(query: $query) {
      id
      manufacturer
      name
    }
  }
`;
